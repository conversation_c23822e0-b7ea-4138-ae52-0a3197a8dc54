package model

import (
	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	"github.com/goccy/go-json"
	"time"
)

// UserActivityData 玩家活动数据结构
type UserActivityData struct {
	Metrics       map[int32]int64 `json:"metrics"`        // 指标，key为指标类型，value为数值
	MetricsType   int32           `json:"metrics_type"`   // 指标类型：累加 最大值...
	ClaimedStages map[int32]int64 `json:"claimed_stages"` // 已领取阶段记录，key为阶段ID，value为领取时间戳
	UpdatedAt     int64           `json:"updated_at"`     // 最后更新时间戳
}

// MetricOperationType 指标操作类型
type MetricOperationType int32

// NewUserActivityData 创建新的用户活动数据
func NewUserActivityData() *UserActivityData {
	return &UserActivityData{
		Metrics:       make(map[int32]int64),
		MetricsType:   0,
		ClaimedStages: make(map[int32]int64),
		UpdatedAt:     time.Now().Unix(),
	}
}

// IsStageClaimedStage 检查阶段是否已领取
func (uad *UserActivityData) IsStageClaimedStage(stageId int32) bool {
	if uad.ClaimedStages == nil {
		return false
	}
	_, exists := uad.ClaimedStages[stageId]
	return exists
}

// GetClaimedStagesList 获取已领取阶段列表
func (uad *UserActivityData) GetClaimedStagesList() []int32 {
	if uad.ClaimedStages == nil {
		return []int32{}
	}

	stages := make([]int32, 0, len(uad.ClaimedStages))
	for stageId := range uad.ClaimedStages {
		stages = append(stages, stageId)
	}
	return stages
}

// ToJSON 将用户活动数据序列化为JSON字符串
func (uad *UserActivityData) ToJSON() ([]byte, error) {
	// 更新时间戳
	uad.UpdatedAt = time.Now().Unix()
	return json.Marshal(uad)
}

// FromJSON 从JSON字符串反序列化用户活动数据
func (uad *UserActivityData) FromJSON(data []byte) error {
	return json.Unmarshal(data, uad)
}

// FromJSONString 从JSON字符串反序列化用户活动数据
func (uad *UserActivityData) FromJSONString(data string) error {
	return json.Unmarshal([]byte(data), uad)
}

// ToActivityProgress 将用户活动数据转换为ActivityProgress protobuf结构
func (uad *UserActivityData) ToActivityProgress(activityId uint64, cycleId int32, cycleEndTime int64) *activityPB.ActivityProgress {
	return &activityPB.ActivityProgress{
		ActivityId:     activityId,
		CurrentCycleId: cycleId,
		CycleEndTime:   cycleEndTime,
		Metrics:        uad.Metrics,
		ClaimedRecords: uad.GetClaimedStagesList(),
	}
}
